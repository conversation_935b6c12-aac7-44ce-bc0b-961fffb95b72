//@version=6
indicator("vickymosafan - [Pro Edition] #3", overlay=false)

// =============================================================================
// 🌊 WAVELET INDICATOR INPUTS
// =============================================================================
shortLength = input(12, "Short Scale Length", tooltip="Fast wavelet component (8-20 recommended). Lower = more sensitive to quick moves", group="🌊 Wavelet Settings")
longLength = input(23, "Long Scale Length", tooltip="Slow wavelet component (20-50 recommended). Higher = smoother trend detection", group="🌊 Wavelet Settings")
smoothing = input(8, "Final Signal Smoothing", tooltip="Smooths the final output signal (5-15 recommended). Higher = less noise but slower response", group="📊 Signal Processing")
lookback = input.int(1000, "Normalization Lookback", maxval=1000, minval=1, step=1, tooltip="Bars used for signal normalization (500-1000). More bars = more stable normalization", group="📊 Signal Processing")
atrLength = input(8, "ATR Length", tooltip="Average True Range period for volatility adjustment (8-21 recommended)", group="🌊 Wavelet Settings")

// Wavelet controls
useWavelet = input.bool(true, "Enable Magical Wavelet", tooltip="Turn ON for advanced wavelet analysis, OFF for simple EMA-based signals", group="🌊 Wavelet Settings")
noiseReduction = input.bool(true, "Enable Noise Reduction", tooltip="Reduces market noise for cleaner signals. Recommended: ON", group="🌊 Wavelet Settings")
wnMin = input(1, "NoiseR Min Length", tooltip="Minimum period for noise reduction (1-3). Lower = more aggressive filtering", group="🌊 Wavelet Settings")
wnMax = input(2, "NoiseR Max Length", tooltip="Maximum period for noise reduction (2-5). Higher = smoother but slower", group="🌊 Wavelet Settings")

// =============================================================================
// 💥 ENHANCED BREAK RETEST INDICATOR INPUTS
// =============================================================================
enableBreakRetest = input.bool(true, "Enable Break Retest Analysis", tooltip="Detects when price breaks key levels and retests them. Essential for breakout trading!", group="💥 Break Retest Settings")
brLookback = input.int(20, "Break Retest Lookback", minval=5, maxval=100, tooltip="Bars to look back for pivot highs/lows (10-30 recommended). Higher = stronger levels but fewer signals", group="💥 Break Retest Settings")
brSensitivity = input.float(0.5, "Break Sensitivity", minval=0.1, maxval=2.0, step=0.1, tooltip="How easily breaks are detected (0.3-0.8 recommended). Lower = more sensitive, more signals", group="💥 Break Retest Settings")
retestTolerance = input.float(0.2, "Retest Tolerance %", minval=0.05, maxval=1.0, step=0.05, tooltip="Price distance allowed for retest detection (0.1-0.5%). Higher = more retest signals", group="💥 Break Retest Settings")
brConfirmationBars = input.int(3, "Break Confirmation Bars", minval=1, maxval=10, tooltip="Bars needed to confirm a break (2-5 recommended). More bars = stronger confirmation", group="💥 Break Retest Settings")
retestConfirmationBars = input.int(2, "Retest Confirmation Bars", minval=1, maxval=5, tooltip="Bars needed to confirm a retest (1-3 recommended). More bars = stronger confirmation", group="💥 Break Retest Settings")

// Enhanced Accuracy Settings
enableVolumeConfirmation = input.bool(true, "Volume Confirmation", tooltip="Requires above-average volume for break confirmation. Increases accuracy significantly!", group="💥 Break Retest Settings")
volumeThreshold = input.float(1.5, "Volume Threshold Multiplier", minval=1.0, maxval=3.0, step=0.1, tooltip="Volume must be X times average (1.2-2.0 recommended). Higher = stronger confirmation", group="💥 Break Retest Settings")
enableStrengthFilter = input.bool(true, "Level Strength Filter", tooltip="Only considers strong S/R levels that have been tested multiple times", group="💥 Break Retest Settings")
minTouches = input.int(2, "Minimum Level Touches", minval=1, maxval=5, tooltip="Minimum times a level must be touched to be considered strong (2-3 recommended)", group="💥 Break Retest Settings")
enableMultiTimeframe = input.bool(true, "Multi-Timeframe Analysis", tooltip="Considers higher timeframe S/R levels for better accuracy", group="💥 Break Retest Settings")

// =============================================================================
// 🤖 ADAPTIVE NEURAL NETWORK INPUTS
// =============================================================================
enableAI = input.bool(true, "Enable AI Prediction Engine", tooltip="Advanced neural network that learns from market patterns. Highly recommended for better accuracy!", group="🤖 AI Neural Network")
adaptivePeriod = input(50, "Adaptive Normalization Period", tooltip="Period for AI data normalization (30-100). Higher = more stable but slower adaptation", group="🤖 AI Neural Network")
adaptationRate = input.float(0.08, 'Neural Adaptation Rate', step=0.01, maxval=0.15, tooltip="How fast AI learns from new data (0.05-0.12 recommended). Higher = faster learning but more volatile", group="🤖 AI Neural Network")

// AI Feature Parameters
momentum_period = input.int(29, "Momentum Detector Length", tooltip="RSI period for momentum detection (14-50). Standard RSI uses 14", group="🧠 AI Features")
volatility_period = input.int(45, "Volatility Detector Length", tooltip="CCI period for volatility analysis (20-100). Higher = smoother volatility detection", group="🧠 AI Features")
trend_strength_period = input.int(35, "Trend Strength Length", tooltip="DMI period for trend analysis (14-50). Higher = stronger trend confirmation", group="🧠 AI Features")
oscillation_period = input.int(35, "Oscillation Detector Length", tooltip="Period for price oscillation analysis (20-50). Detects market cycles", group="🧠 AI Features")
velocity_period = input(30, "Price Velocity Length", tooltip="Period for price velocity calculation (20-50). Measures speed of price movement", group="🧠 AI Features")
resistance_factor = input.float(3.2, "Dynamic Resistance Factor", step=0.1, tooltip="SuperTrend multiplier for resistance detection (2.0-5.0). Higher = stronger resistance levels", group="🧠 AI Features")
resistance_period = input.int(2, "Resistance Detection Period", step=1, tooltip="SuperTrend period for resistance (1-5). Lower = more sensitive resistance detection", group="🧠 AI Features")

// =============================================================================
// 📈 ADDITIONAL TECHNICAL INDICATORS
// =============================================================================
enableMACD = input.bool(true, "Enable MACD Analysis", tooltip="Moving Average Convergence Divergence - excellent for trend changes and momentum", group="📈 Additional Indicators")
macdFast = input.int(12, "MACD Fast Length", tooltip="Fast EMA period (8-15 recommended). Standard MACD uses 12", group="📈 Additional Indicators")
macdSlow = input.int(26, "MACD Slow Length", tooltip="Slow EMA period (20-35 recommended). Standard MACD uses 26", group="📈 Additional Indicators")
macdSignal = input.int(9, "MACD Signal Length", tooltip="Signal line EMA period (7-12 recommended). Standard MACD uses 9", group="📈 Additional Indicators")

enableBollinger = input.bool(true, "Enable Bollinger Bands", tooltip="Bollinger Bands - great for volatility and mean reversion analysis", group="📈 Additional Indicators")
bbLength = input.int(20, "BB Length", tooltip="Bollinger Bands period (15-25 recommended). Standard BB uses 20", group="📈 Additional Indicators")
bbMult = input.float(2.0, "BB Multiplier", tooltip="Standard deviation multiplier (1.5-2.5). Higher = wider bands, fewer signals", group="📈 Additional Indicators")

enableStochastic = input.bool(true, "Enable Stochastic", tooltip="Stochastic Oscillator - identifies overbought/oversold conditions", group="📈 Additional Indicators")
stochK = input.int(14, "Stochastic %K", tooltip="Stochastic K period (10-21 recommended). Standard uses 14", group="📈 Additional Indicators")
stochD = input.int(3, "Stochastic %D", tooltip="Stochastic D smoothing (3-5 recommended). Higher = smoother signals", group="📈 Additional Indicators")

enableWilliamsR = input.bool(true, "Enable Williams %R", tooltip="Williams %R - similar to Stochastic but inverted scale", group="📈 Additional Indicators")
williamsLength = input.int(14, "Williams %R Length", tooltip="Williams %R period (10-21 recommended). Standard uses 14", group="📈 Additional Indicators")

enableFisherTransform = input.bool(true, "Enable Fisher Transform", tooltip="Fisher Transform - converts prices to Gaussian normal distribution for better signals", group="📈 Additional Indicators")
fisherLength = input.int(10, "Fisher Transform Length", tooltip="Fisher Transform period (8-15 recommended). Lower = more sensitive signals", group="📈 Additional Indicators")

// 🧠 Neural Network Weights (Advanced Users Only)
// These weights determine how much influence each indicator has on the final AI prediction
// Higher values = more influence. Recommended to keep default values unless you're experienced
alpha_momentum = 1      // RSI momentum weight (0.5-2.0 range)
beta_volatility = 4     // CCI volatility weight (2-6 range) - High influence
gamma_trend = 1         // DMI trend weight (0.5-2.0 range)
delta_oscillation = 2   // Oscillation weight (1-3 range)
epsilon_velocity = 5    // Price velocity weight (3-7 range) - High influence
zeta_resistance = 4     // SuperTrend resistance weight (2-6 range)

// Additional Technical Indicator Weights
theta_macd = 3          // MACD weight (2-5 range)
iota_bollinger = 2      // Bollinger Bands weight (1-4 range)
kappa_stochastic = 3    // Stochastic weight (2-5 range)
lambda_williams = 2     // Williams %R weight (1-4 range)
mu_fisher = 4           // Fisher Transform weight (2-6 range)

// Break Retest Analysis Weight
nu_break_retest = 6     // Break/Retest weight (4-8 range) - Highest influence

// 🌟 Neon Color Palette - Optimized for Dark Mode
upper_col = input.color(#00ff41, "Neon Green (Bullish)", inline = "neon_colors", group="🌟 Neon Display", tooltip="Main color for bullish/up signals. Bright neon green for high visibility")
lower_col = input.color(#ff0080, "Neon Pink (Bearish)", inline = "neon_colors", group="🌟 Neon Display", tooltip="Main color for bearish/down signals. Vibrant pink for clear distinction")
neutral_col = input.color(#ffff00, "Electric Yellow (Neutral)", inline = "neon_colors2", group="🌟 Neon Display", tooltip="Color for neutral/sideways market conditions")
break_col = input.color(#00ffff, "Cyan Glow (Breakouts)", inline = "neon_colors2", group="🌟 Neon Display", tooltip="Special color for breakout signals - highly important events!")
retest_col = input.color(#ff6600, "Orange Pulse (Retests)", inline = "neon_colors3", group="🌟 Neon Display", tooltip="Color for retest signals - when price returns to test broken levels")

// Additional Neon Accent Colors
accent_col = input.color(#9d00ff, "Purple Accent", inline = "neon_colors3", group="🌟 Neon Display", tooltip="Accent color for special effects and highlights")
glow_col = input.color(#ffffff, "White Glow", inline = "neon_colors4", group="🌟 Neon Display", tooltip="White glow color for maximum contrast and visibility")
shadow_col = input.color(#1a1a1a, "Dark Shadow", inline = "neon_colors4", group="🌟 Neon Display", tooltip="Dark background color for panels and shadows")

// 🎨 Enhanced Neon Display Options
candleStyle = input.string("Neon Glow", "Candle Style", options=["Original", "Enhanced", "Gradient", "Neon Glow", "Electric"],
           tooltip="Choose your preferred visual style:\n• Original: Classic colors\n• Enhanced: Improved visibility\n• Gradient: Smooth color transitions\n• Neon Glow: Full neon effect (recommended!)\n• Electric: High-intensity pulsing",
           group="🌟 Neon Display")
showVolume = input.bool(true, "Show Volume Glow", tooltip="Volume affects candle glow intensity. Higher volume = brighter glow", group="🌟 Neon Display")
showMomentum = input.bool(true, "Show Momentum Pulse", tooltip="Shows momentum as pulsing histogram bars below main signal", group="🌟 Neon Display")
showBreakRetest = input.bool(true, "Show Break Retest Neon", tooltip="Displays special neon lines when breakouts and retests occur", group="🌟 Neon Display")
glowIntensity = input.float(0.8, "Neon Glow Intensity", minval=0.1, maxval=1.0, step=0.1,
              tooltip="Controls overall glow brightness (0.1-1.0):\n• 0.1-0.3: Subtle glow\n• 0.4-0.7: Moderate glow\n• 0.8-1.0: Maximum neon effect",
              group="🌟 Neon Display")
pulseEffect = input.bool(true, "Enable Pulse Effect", tooltip="Adds animated pulsing to break/retest signals and zero line. Creates dynamic visual effects!", group="🌟 Neon Display")

// =============================================================================
// 📏 NEON REFERENCE GRID SETTINGS
// =============================================================================
showHLines = input.bool(true, "Show Neon Grid Lines", tooltip="Displays horizontal reference lines with signal strength labels. Helps identify key levels!", group="🌟 Neon Display")
hlineColor = input.color(color.new(#00ffff, 60), "Grid Glow Color", tooltip="Base color for the reference grid lines", group="🌟 Neon Display")
hlineStyle = input.string("Dashed", "Grid Line Style", options=["Solid", "Dashed", "Dotted"], tooltip="Visual style of grid lines. Dashed is recommended for better chart readability", group="🌟 Neon Display")
gridGlow = input.bool(true, "Enable Grid Glow Effect", tooltip="Adds glow effect to grid lines for enhanced neon appearance", group="🌟 Neon Display")

// =============================================================================
// ENHANCED BREAK RETEST DETECTION FUNCTIONS
// =============================================================================

// Enhanced structure detection with multiple methods
detect_structure_enhanced(src, len) =>
    // Standard pivot detection
    pivot_high = ta.pivothigh(src, len, len)
    pivot_low = ta.pivotlow(src, len, len)

    // Fractal-based detection for better accuracy
    fractal_high = ta.pivothigh(high, len, len)
    fractal_low = ta.pivotlow(low, len, len)

    // Use the most significant level
    final_high = not na(fractal_high) ? fractal_high : pivot_high
    final_low = not na(fractal_low) ? fractal_low : pivot_low

    [final_high, final_low]

// Level strength calculation
calculate_level_strength(level, lookback_period) =>
    if na(level)
        0
    else
        touches = 0
        for i = 1 to lookback_period
            price_range = math.abs(high[i] - low[i])
            tolerance_range = price_range * 0.5
            if math.abs(high[i] - level) <= tolerance_range or math.abs(low[i] - level) <= tolerance_range
                touches += 1
        touches

// Volume-confirmed break detection
detect_break_enhanced(current_price, structure_level, is_resistance, sensitivity, volume_confirmed) =>
    break_threshold = structure_level * (1 + (is_resistance ? sensitivity / 100 : -sensitivity / 100))
    price_break = is_resistance ? current_price > break_threshold : current_price < break_threshold

    // Volume confirmation
    volume_avg = ta.sma(volume, 20)
    volume_spike = enableVolumeConfirmation ? volume > (volume_avg * volumeThreshold) : true

    break_occurred = price_break and (not enableVolumeConfirmation or volume_spike)
    break_occurred

// Enhanced retest detection with multiple confirmations
detect_retest_enhanced(current_price, structure_level, tolerance) =>
    retest_upper = structure_level * (1 + tolerance / 100)
    retest_lower = structure_level * (1 - tolerance / 100)

    // Price-based retest
    price_retest = current_price >= retest_lower and current_price <= retest_upper

    // Wick-based retest (more sensitive)
    wick_retest = (low <= retest_upper and low >= retest_lower) or (high <= retest_upper and high >= retest_lower)

    retest_occurred = price_retest or wick_retest
    retest_occurred

// Multi-timeframe level detection
get_htf_levels() =>
    htf_high = request.security(syminfo.tickerid, "1H", ta.pivothigh(high, 10, 10))
    htf_low = request.security(syminfo.tickerid, "1H", ta.pivotlow(low, 10, 10))
    [htf_high, htf_low]

// =============================================================================
// ENHANCED BREAK RETEST CALCULATIONS
// =============================================================================

// Enhanced structure detection
[pivot_high, pivot_low] = detect_structure_enhanced(hlc3, brLookback)

// Multi-timeframe levels
[htf_resistance, htf_support] = enableMultiTimeframe ? get_htf_levels() : [na, na]

// Track multiple resistance and support levels
var array<float> resistance_levels = array.new<float>()
var array<float> support_levels = array.new<float>()
var array<int> resistance_strength = array.new<int>()
var array<int> support_strength = array.new<int>()

// Current active levels
var float recent_resistance = na
var float recent_support = na
var int resistance_level_strength = 0
var int support_level_strength = 0

// Break and retest tracking
var bool resistance_broken = false
var bool support_broken = false
var bool resistance_retested = false
var bool support_retested = false
var int bars_since_resistance_break = 0
var int bars_since_support_break = 0

// Enhanced level management
if not na(pivot_high)
    level_strength = calculate_level_strength(pivot_high, brLookback * 2)

    // Only consider strong levels if filter is enabled
    if not enableStrengthFilter or level_strength >= minTouches
        // Add to resistance levels array
        if array.size(resistance_levels) >= 5
            array.shift(resistance_levels)
            array.shift(resistance_strength)
        array.push(resistance_levels, pivot_high)
        array.push(resistance_strength, level_strength)

        // Update current resistance if stronger or closer
        if na(recent_resistance) or level_strength > resistance_level_strength or math.abs(close - pivot_high) < math.abs(close - recent_resistance)
            recent_resistance := pivot_high
            resistance_level_strength := level_strength
            resistance_broken := false
            resistance_retested := false
            bars_since_resistance_break := 0

if not na(pivot_low)
    level_strength = calculate_level_strength(pivot_low, brLookback * 2)

    // Only consider strong levels if filter is enabled
    if not enableStrengthFilter or level_strength >= minTouches
        // Add to support levels array
        if array.size(support_levels) >= 5
            array.shift(support_levels)
            array.shift(support_strength)
        array.push(support_levels, pivot_low)
        array.push(support_strength, level_strength)

        // Update current support if stronger or closer
        if na(recent_support) or level_strength > support_level_strength or math.abs(close - pivot_low) < math.abs(close - recent_support)
            recent_support := pivot_low
            support_level_strength := level_strength
            support_broken := false
            support_retested := false
            bars_since_support_break := 0

// Consider HTF levels if enabled
if enableMultiTimeframe
    if not na(htf_resistance) and (na(recent_resistance) or math.abs(close - htf_resistance) < math.abs(close - recent_resistance))
        recent_resistance := htf_resistance
        resistance_level_strength := 5  // HTF levels get high strength

    if not na(htf_support) and (na(recent_support) or math.abs(close - htf_support) < math.abs(close - recent_support))
        recent_support := htf_support
        support_level_strength := 5  // HTF levels get high strength

// Enhanced break detection with multiple confirmations
if not na(recent_resistance) and not resistance_broken
    volume_confirmed = enableVolumeConfirmation ? volume > ta.sma(volume, 20) * volumeThreshold : true
    if detect_break_enhanced(close, recent_resistance, true, brSensitivity, volume_confirmed)
        resistance_broken := true
        bars_since_resistance_break := 1
    else
        bars_since_resistance_break := 0

if not na(recent_support) and not support_broken
    volume_confirmed = enableVolumeConfirmation ? volume > ta.sma(volume, 20) * volumeThreshold : true
    if detect_break_enhanced(close, recent_support, false, brSensitivity, volume_confirmed)
        support_broken := true
        bars_since_support_break := 1
    else
        bars_since_support_break := 0

// Update bars since break
if resistance_broken and bars_since_resistance_break > 0
    bars_since_resistance_break := bars_since_resistance_break + 1

if support_broken and bars_since_support_break > 0
    bars_since_support_break := bars_since_support_break + 1

// Enhanced retest detection
if resistance_broken and not resistance_retested and bars_since_resistance_break > brConfirmationBars
    if detect_retest_enhanced(close, recent_resistance, retestTolerance)
        resistance_retested := true

if support_broken and not support_retested and bars_since_support_break > brConfirmationBars
    if detect_retest_enhanced(close, recent_support, retestTolerance)
        support_retested := true

// Enhanced Break Retest signals with strength weighting
resistance_break_signal = resistance_broken and bars_since_resistance_break == brConfirmationBars
support_break_signal = support_broken and bars_since_support_break == brConfirmationBars
resistance_retest_signal = resistance_retested and resistance_retested[1] == false
support_retest_signal = support_retested and support_retested[1] == false

// Calculate signal strength based on level strength and volume
resistance_signal_strength = resistance_level_strength / 5.0  // Normalize to 0-1
support_signal_strength = support_level_strength / 5.0       // Normalize to 0-1

// Volume strength factor
volume_strength = enableVolumeConfirmation ? math.min(volume / ta.sma(volume, 20), 3.0) / 3.0 : 1.0

// Enhanced Break Retest normalized signal with strength weighting
br_signal = 0.0
if enableBreakRetest
    br_signal := 0.0
    if resistance_break_signal
        br_signal := 1.0 * resistance_signal_strength * volume_strength
    else if support_break_signal
        br_signal := -1.0 * support_signal_strength * volume_strength
    else if resistance_retest_signal
        br_signal := 0.8 * resistance_signal_strength
    else if support_retest_signal
        br_signal := -0.8 * support_signal_strength
    else if resistance_broken and not resistance_retested
        br_signal := 0.3 * resistance_signal_strength
    else if support_broken and not support_retested
        br_signal := -0.3 * support_signal_strength

// Additional confirmation signals
strong_resistance_break = resistance_break_signal and resistance_level_strength >= 3 and volume_strength > 0.7
strong_support_break = support_break_signal and support_level_strength >= 3 and volume_strength > 0.7
weak_break_warning = (resistance_broken or support_broken) and volume_strength < 0.5

// =============================================================================
// WAVELET FUNCTIONS & CALCULATIONS
// =============================================================================
atr = ta.atr(atrLength)

// Wavelet decomposition
pi = 3.14159265359
wavelet(src, len) =>
    alpha = (1 - math.sin(2 * pi / len)) / math.cos(2 * pi / len)
    hp = 0.0
    hp := (1 - alpha/2) * (src - src[1]) + (1 - alpha) * nz(hp[1])
    ta.ema(hp, 3)

// Multi-scale decomposition with enhanced sensitivity
fastComponent = useWavelet ? wavelet(close, shortLength) : ta.ema(close, shortLength)
slowComponent = useWavelet ? wavelet(close, longLength) : ta.ema(close, longLength)
scaleRatio = longLength / shortLength
rawSignal = (fastComponent - slowComponent) * scaleRatio

// Noise reduction
wn(src) =>
    maFast = ta.ema(src, wnMin)
    maSlow = ta.ema(src, wnMax)
    noiseReduction ? (maFast + maSlow) / 2 : src

smoothedSignal = wn(rawSignal)

// Normalization with scale preservation
atrAdjustedSignal = smoothedSignal / math.max(atr * scaleRatio, 0.001)
absMax = ta.highest(math.abs(atrAdjustedSignal), lookback)
waveletNormalized = atrAdjustedSignal / math.max(absMax, 0.001)
waveletNormalized := math.min(math.max(waveletNormalized, -1), 1)

// =============================================================================
// ADDITIONAL INDICATORS CALCULATIONS
// =============================================================================

// MACD Analysis
[macdLine, signalLine, histLine] = ta.macd(close, macdFast, macdSlow, macdSignal)
macdNormalized = enableMACD ? ta.stoch(macdLine, macdLine, macdLine, 50) / 100 - 0.5 : 0

// Bollinger Bands Analysis
basis = ta.sma(close, bbLength)
dev = bbMult * ta.stdev(close, bbLength)
upper_bb = basis + dev
lower_bb = basis - dev
bb_position = enableBollinger ? (close - lower_bb) / (upper_bb - lower_bb) - 0.5 : 0

// Stochastic Analysis
k = ta.stoch(close, high, low, stochK)
d = ta.sma(k, stochD)
stochNormalized = enableStochastic ? (k / 100) - 0.5 : 0

// Williams %R Analysis
williams_r = ta.wpr(williamsLength)
williamsNormalized = enableWilliamsR ? (williams_r + 50) / 100 - 0.5 : 0

// Fisher Transform Analysis
fisher_transform = 0.0
if enableFisherTransform
    price_normalized = 0.33 * 2 * ((close - ta.lowest(close, fisherLength)) / (ta.highest(close, fisherLength) - ta.lowest(close, fisherLength)) - 0.5)
    smooth_price = ta.ema(price_normalized, 5)
    fisher_transform := 0.5 * math.log((1 + smooth_price) / (1 - smooth_price))
    fisher_transform := ta.ema(fisher_transform, 3) / 3

// =============================================================================
// ADAPTIVE NEURAL NETWORK FUNCTIONS & CALCULATIONS
// =============================================================================

// Adaptive Data Standardization Engine
standardize_data(source_data) =>
    mean_baseline = ta.sma(source_data, adaptivePeriod)
    deviation = ta.stdev(source_data, adaptivePeriod)
    standardized = (source_data - mean_baseline) / math.max(deviation, 0.0001)
    standardized

// Enhanced Neural Activation Function
neural_activation_enhanced(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, bias, w1, w2, w3, w4, w5, w6, w7, w8, w9, w10, w11, w12) =>
    neural_input = bias + w1 * f1 + w2 * f2 + w3 * f3 + w4 * f4 + w5 * f5 + w6 * f6 + w7 * f7 + w8 * f8 + w9 * f9 + w10 * f10 + w11 * f11 + w12 * f12
    activation_output = 1 / (1 + math.exp(-neural_input))
    activation_output

// Prediction Error Calculator
prediction_error(actual_target, predicted_output) =>
    safe_predicted = math.max(math.min(predicted_output, 0.9999), 0.0001)
    error = -actual_target * math.log(safe_predicted) - (1 - actual_target) * math.log(1 - safe_predicted)
    error

// Advanced Feature Engineering Module
momentum_detector = ta.rsi(close, momentum_period)
volatility_detector = ta.cci(hlc3, volatility_period)
[trend_positive, trend_negative, _] = ta.dmi(trend_strength_period, 10)

// Custom Oscillation Detector 
highest_position(length) =>
    bars_since_high = ta.highestbars(high, length)
    oscillation_up = ((length + bars_since_high) / length) * 100
    oscillation_up

lowest_position(length) =>
    bars_since_low = ta.lowestbars(low, length)
    oscillation_down = ((length + bars_since_low) / length) * 100
    oscillation_down

oscillation_up = highest_position(oscillation_period)
oscillation_down = lowest_position(oscillation_period)

// Price Velocity Analyzer
velocity_fast = ta.ema(close, velocity_period)
velocity_slow = ta.ema(close, velocity_period - 10)

// Dynamic Resistance Detection System
[resistance_level, resistance_direction] = ta.supertrend(resistance_factor, resistance_period)

// Enhanced Neural Network Weight Management System
var float bias_node = 1.0
var float learning_adj_momentum = 0.0
var float learning_adj_volatility = 0.0
var float learning_adj_trend = 0.0
var float learning_adj_oscillation = 0.0
var float learning_adj_velocity = 0.0
var float learning_adj_resistance = 0.0
var float learning_adj_macd = 0.0
var float learning_adj_bollinger = 0.0
var float learning_adj_stochastic = 0.0
var float learning_adj_williams = 0.0
var float learning_adj_fisher = 0.0
var float learning_adj_break_retest = 0.0

// Target Variable Generation 
market_direction = standardize_data(close) > 0 ? 1 : 0

// Enhanced Feature Vector Construction 
feature_momentum = momentum_detector > 50 ? 1 : 0
feature_volatility = 0.5
if ta.crossover(volatility_detector, 100)
    feature_volatility := 1
if ta.crossunder(volatility_detector, -100)
    feature_volatility := 0

feature_trend = trend_positive > trend_negative ? 1 : 0
feature_oscillation = oscillation_up > oscillation_down ? 1 : 0
feature_velocity = velocity_fast > velocity_slow ? 1 : 0
feature_resistance = resistance_direction == -1 ? 1 : 0

// Additional features
feature_macd = macdLine > signalLine ? 1 : 0
feature_bollinger = close > basis ? 1 : 0
feature_stochastic = k > d ? 1 : 0
feature_williams = williams_r > -50 ? 1 : 0
feature_fisher = fisher_transform > 0 ? 1 : 0

// Break Retest features
feature_break_retest = br_signal > 0 ? 1 : 0

// Enhanced Adaptive Neural Network Processing
ai_prediction = 0.0
if enableAI
    current_momentum = alpha_momentum + learning_adj_momentum
    current_volatility = beta_volatility + learning_adj_volatility
    current_trend = gamma_trend + learning_adj_trend
    current_oscillation = delta_oscillation + learning_adj_oscillation
    current_velocity = epsilon_velocity + learning_adj_velocity
    current_resistance = zeta_resistance + learning_adj_resistance
    current_macd = theta_macd + learning_adj_macd
    current_bollinger = iota_bollinger + learning_adj_bollinger
    current_stochastic = kappa_stochastic + learning_adj_stochastic
    current_williams = lambda_williams + learning_adj_williams
    current_fisher = mu_fisher + learning_adj_fisher
    current_break_retest = nu_break_retest + learning_adj_break_retest
    
    // Forward Pass: Generate Initial Prediction with Enhanced Features
    initial_prediction = neural_activation_enhanced(feature_momentum, feature_volatility, feature_trend, feature_oscillation, feature_velocity, feature_resistance, feature_macd, feature_bollinger, feature_stochastic, feature_williams, feature_fisher, feature_break_retest, bias_node, current_momentum, current_volatility, current_trend, current_oscillation, current_velocity, current_resistance, current_macd, current_bollinger, current_stochastic, current_williams, current_fisher, current_break_retest)
    
    // Backward Pass: Enhanced Adaptive Weight Adjustment 
    prediction_gradient = initial_prediction - market_direction
    learning_adj_momentum := learning_adj_momentum - adaptationRate * prediction_gradient * feature_momentum
    learning_adj_volatility := learning_adj_volatility - adaptationRate * prediction_gradient * feature_volatility
    learning_adj_trend := learning_adj_trend - adaptationRate * prediction_gradient * feature_trend
    learning_adj_oscillation := learning_adj_oscillation - adaptationRate * prediction_gradient * feature_oscillation
    learning_adj_velocity := learning_adj_velocity - adaptationRate * prediction_gradient * feature_velocity
    learning_adj_resistance := learning_adj_resistance - adaptationRate * prediction_gradient * feature_resistance
    learning_adj_macd := learning_adj_macd - adaptationRate * prediction_gradient * feature_macd
    learning_adj_bollinger := learning_adj_bollinger - adaptationRate * prediction_gradient * feature_bollinger
    learning_adj_stochastic := learning_adj_stochastic - adaptationRate * prediction_gradient * feature_stochastic
    learning_adj_williams := learning_adj_williams - adaptationRate * prediction_gradient * feature_williams
    learning_adj_fisher := learning_adj_fisher - adaptationRate * prediction_gradient * feature_fisher
    learning_adj_break_retest := learning_adj_break_retest - adaptationRate * prediction_gradient * feature_break_retest
    
    // Final Prediction with Enhanced Adapted Weights
    final_prediction = neural_activation_enhanced(feature_momentum, feature_volatility, feature_trend, feature_oscillation, feature_velocity, feature_resistance, feature_macd, feature_bollinger, feature_stochastic, feature_williams, feature_fisher, feature_break_retest, bias_node, current_momentum, current_volatility, current_trend, current_oscillation, current_velocity, current_resistance, current_macd, current_bollinger, current_stochastic, current_williams, current_fisher, current_break_retest)
    
    // Transform to Bipolar Signal Range (-1 to 1)
    ai_prediction := (final_prediction - 0.5) * 2

// =============================================================================
// ENHANCED SIGNAL FUSION WITH BREAK RETEST
// =============================================================================

// Calculate composite signal from additional indicators
additionalSignalsComposite = 0.0
signalCount = 0

if enableMACD
    additionalSignalsComposite += macdNormalized
    signalCount += 1

if enableBollinger
    additionalSignalsComposite += bb_position
    signalCount += 1

if enableStochastic
    additionalSignalsComposite += stochNormalized
    signalCount += 1

if enableWilliamsR
    additionalSignalsComposite += williamsNormalized
    signalCount += 1

if enableFisherTransform
    additionalSignalsComposite += fisher_transform
    signalCount += 1

if enableBreakRetest
    additionalSignalsComposite += br_signal
    signalCount += 1

additionalSignalsNormalized = signalCount > 0 ? additionalSignalsComposite / signalCount : 0

// Enhanced signal fusion
rawFinalSignal = 0.0
componentCount = 0

if useWavelet
    rawFinalSignal += waveletNormalized
    componentCount += 1

if enableAI
    rawFinalSignal += ai_prediction
    componentCount += 1

if signalCount > 0
    rawFinalSignal += additionalSignalsNormalized
    componentCount += 1

if componentCount == 0
    rawFinalSignal := ta.mom(close, 14) / close
else
    rawFinalSignal := rawFinalSignal / componentCount

// APPLY SMOOTHING TO ENTIRE FINAL SIGNAL 
smoothedFinalSignal = ta.ema(rawFinalSignal, smoothing)

// ENHANCED ADAPTIVE NORMALIZATION
recentVolatility = ta.stdev(smoothedFinalSignal, lookback)
longTermVolatility = ta.stdev(smoothedFinalSignal, lookback * 2)
adaptiveScale = recentVolatility / math.max(longTermVolatility, 0.0001)

// Apply adaptive scaling 
finalSignal = smoothedFinalSignal * adaptiveScale
finalSignal := math.min(math.max(finalSignal, -2), 2)  

// Enhanced signal strength calculation
signalStrength = math.abs(finalSignal)
signalMomentum = finalSignal - finalSignal[1]

// =============================================================================
// NEON GRID REFERENCE LINES
// =============================================================================

// Neon grid colors with glow effect
neonGridColor1 = showHLines ? color.new(#00ffff, gridGlow ? 40 : 70) : color.new(color.white, 100)
neonGridColor2 = showHLines ? color.new(#ff00ff, gridGlow ? 50 : 75) : color.new(color.white, 100)
neonGridColor3 = showHLines ? color.new(#ffff00, gridGlow ? 45 : 70) : color.new(color.white, 100)
zeroNeonColor = showHLines ? color.new(#ffffff, gridGlow ? 20 : 60) : color.new(color.white, 100)

// Pulsing effect for zero line
pulseAlpha = pulseEffect ? math.abs(math.sin(bar_index * 0.1)) * 40 + 20 : 30
zeroLinePulse = showHLines ? color.new(#00ff41, pulseAlpha) : color.new(color.white, 100)

plot(1.5, "⚡ EXTREME BULL", color = neonGridColor1, linewidth = 1)
plot(1.0, "🔥 STRONG BULL", color = neonGridColor2, linewidth = 1)
plot(0.5, "✨ BULL ZONE", color = neonGridColor1, linewidth = 1)
plot(0.25, "🌟 WEAK BULL", color = neonGridColor2, linewidth = 1)
plot(-0.25, "💫 WEAK BEAR", color = neonGridColor2, linewidth = 1)
plot(-0.5, "🔴 BEAR ZONE", color = neonGridColor1, linewidth = 1)
plot(-1.0, "🩸 STRONG BEAR", color = neonGridColor2, linewidth = 1)
plot(-1.5, "⚡ EXTREME BEAR", color = neonGridColor1, linewidth = 1)

// =============================================================================
// NEON VISUALIZATION WITH BREAK RETEST INTEGRATION
// =============================================================================

// Dynamic neon glow intensity based on signal strength
glowBase = math.round(glowIntensity * 100)
strongGlow = math.max(0, glowBase - 20)
mediumGlow = math.max(0, glowBase - 10)
weakGlow = glowBase

// Pulsing break/retest colors
breakPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.15)) * 30 : 0
retestPulse = pulseEffect ? math.abs(math.cos(bar_index * 0.12)) * 25 : 0

// Dynamic neon color calculation with glow effects
dynamicColor = switch
    resistance_break_signal => color.new(break_col, breakPulse)
    support_break_signal => color.new(break_col, breakPulse)
    resistance_retest_signal => color.new(retest_col, retestPulse)
    support_retest_signal => color.new(retest_col, retestPulse)
    finalSignal > 0.75 => color.new(upper_col, strongGlow)
    finalSignal > 0.25 => color.new(upper_col, mediumGlow)
    finalSignal > 0 => color.new(upper_col, weakGlow)
    finalSignal > -0.25 => color.new(neutral_col, weakGlow)
    finalSignal > -0.75 => color.new(lower_col, weakGlow)
    => color.new(lower_col, strongGlow)

// Volume-based neon glow sizing
volumeRatio = showVolume ? math.min(volume / ta.sma(volume, 20), 3) : 1
candleThickness = math.max(1, math.round(volumeRatio))
volumeGlow = showVolume ? math.max(0, 100 - (volumeRatio * 30)) : 50

// Enhanced neon candle color calculation with break retest integration
candleColor = switch candleStyle
    "Neon Glow" =>
        if resistance_break_signal or support_break_signal
            color.new(break_col, breakPulse)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, retestPulse)
        else
            color.new(dynamicColor, volumeGlow)
    "Electric" =>
        electricPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.2)) * 40 : 20
        if resistance_break_signal or support_break_signal
            color.new(glow_col, electricPulse)
        else if resistance_retest_signal or support_retest_signal
            color.new(accent_col, electricPulse)
        else
            color.new(dynamicColor, electricPulse)
    "Enhanced" =>
        if resistance_break_signal or support_break_signal
            color.new(break_col, 10)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, 15)
        else
            dynamicColor
    "Gradient" =>
        if resistance_break_signal or support_break_signal
            color.new(break_col, breakPulse)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, retestPulse)
        else
            gradientIntensity = math.abs(finalSignal) * glowIntensity * 100
            color.new(dynamicColor, 100 - gradientIntensity)
    =>
        if resistance_break_signal or support_break_signal
            break_col
        else if resistance_retest_signal or support_retest_signal
            retest_col
        else
            finalSignal >= 0 ? upper_col : lower_col

// Neon wick and border effects
candleWickColor = switch candleStyle
    "Neon Glow" => color.new(glow_col, 30)
    "Electric" => color.new(accent_col, 40)
    => candleColor

candleBorderColor = switch candleStyle
    "Neon Glow" => color.new(candleColor, 10)
    "Electric" => color.new(glow_col, 20)
    "Enhanced" => color.new(candleColor, 20)
    "Gradient" => candleColor
    => candleColor

// Plot neon signal candles
plotcandle(open, high, low, close,
           title = '🌟 Neon Signal Candles',
           color = candleColor,
           wickcolor = candleWickColor,
           bordercolor = candleBorderColor,
           force_overlay=true)

// Neon signal line plotting with glow effects
mainSignalGlow = pulseEffect ? math.abs(math.sin(bar_index * 0.08)) * 20 : 10
mainSignalColor = switch
    resistance_break_signal or support_break_signal => color.new(break_col, breakPulse)
    resistance_retest_signal or support_retest_signal => color.new(retest_col, retestPulse)
    finalSignal > 1 => color.new(upper_col, mainSignalGlow)
    finalSignal > 0.5 => color.new(upper_col, mainSignalGlow + 10)
    finalSignal > 0 => color.new(upper_col, mainSignalGlow + 20)
    finalSignal > -0.5 => color.new(lower_col, mainSignalGlow + 20)
    finalSignal > -1 => color.new(lower_col, mainSignalGlow + 10)
    => color.new(lower_col, mainSignalGlow)

// Main neon signal line with enhanced thickness
signalLineWidth = math.max(2, math.round(3 * glowIntensity))
p1 = plot(finalSignal, "🌟 Neon Signal", color = mainSignalColor, linewidth = signalLineWidth)
p2 = plot(0, "━━━ Zero Line ━━━", linewidth = 2, color = zeroLinePulse)

// Neon gradient fill with glow effects
fillGlow = math.round(85 - (glowIntensity * 15))
fillPulse = pulseEffect ? math.abs(math.sin(bar_index * 0.05)) * 10 : 0

fillColor = switch
    resistance_break_signal or support_break_signal => color.new(break_col, 60 + fillPulse)
    resistance_retest_signal or support_retest_signal => color.new(retest_col, 65 + fillPulse)
    finalSignal > 0.5 => color.new(upper_col, fillGlow)
    finalSignal > 0 => color.new(upper_col, fillGlow + 5)
    finalSignal > -0.5 => color.new(lower_col, fillGlow + 5)
    => color.new(lower_col, fillGlow)

fill(p1, p2, finalSignal, 0, na, fillColor)

// Neon momentum pulse bars
momentumPulse = pulseEffect ? math.abs(math.cos(bar_index * 0.1)) * 20 : 30
momentumColor = signalMomentum > 0 ? color.new(upper_col, momentumPulse) : color.new(lower_col, momentumPulse)
momentumValue = showMomentum ? signalMomentum * 2 : na
momentumWidth = math.max(1, math.round(2 * glowIntensity))
plot(momentumValue, "💫 Momentum Pulse", color = momentumColor, style = plot.style_histogram, linewidth = momentumWidth)

// Enhanced signal arrows with break retest integration
strongBullishSignal = finalSignal > 0.5 and finalSignal[1] <= 0.5
weakBullishSignal = finalSignal > 0 and finalSignal[1] <= 0 and not strongBullishSignal
strongBearishSignal = finalSignal < -0.5 and finalSignal[1] >= -0.5
weakBearishSignal = finalSignal < 0 and finalSignal[1] >= 0 and not strongBearishSignal

// Break Retest specific signals for plotting
breakBullishSignal = resistance_break_signal and showBreakRetest
breakBearishSignal = support_break_signal and showBreakRetest
retestBullishSignal = resistance_retest_signal and showBreakRetest
retestBearishSignal = support_retest_signal and showBreakRetest

// Neon signal arrows with glow effects
plotshape(strongBullishSignal,
  style=shape.labelup,
  location=location.belowbar,
  color=color.new(upper_col, strongGlow),
  textcolor=glow_col,
  size=size.small,
  text="⭷",
  title="🌟 Strong Bull Neon", force_overlay=true)

plotshape(weakBullishSignal,
  style=shape.labelup,
  location=location.belowbar,
  color=color.new(upper_col, mediumGlow),
  textcolor=color.new(glow_col, 20),
  size=size.tiny,
  text="⬆",
  title="✨ Weak Bull Glow", force_overlay=true)

plotshape(strongBearishSignal,
  style=shape.labeldown,
  location=location.abovebar,
  color=color.new(lower_col, strongGlow),
  textcolor=glow_col,
  size=size.small,
  text="⭸",
  title="🌟 Strong Bear Neon", force_overlay=true)

plotshape(weakBearishSignal,
  style=shape.labeldown,
  location=location.abovebar,
  color=color.new(lower_col, mediumGlow),
  textcolor=color.new(glow_col, 20),
  size=size.tiny,
  text="⬇",
  title="✨ Weak Bear Glow", force_overlay=true)

// Neon Break Retest lines with glow effects
neonLineWidth = math.max(2, math.round(4 * glowIntensity))

plot(series=breakBullishSignal ? high : na,
     title="💥 Resistance Break Neon",
     color=color.new(break_col, breakPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

plot(series=breakBearishSignal ? low : na,
     title="💥 Support Break Neon",
     color=color.new(break_col, breakPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

plot(series=retestBullishSignal ? high : na,
     title="🔄 Resistance Retest Glow",
     color=color.new(retest_col, retestPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

plot(series=retestBearishSignal ? low : na,
     title="🔄 Support Retest Glow",
     color=color.new(retest_col, retestPulse),
     linewidth=neonLineWidth,
     style=plot.style_linebr,
     trackprice=true,
     force_overlay=true)

// =============================================================================
// ENHANCED ALERT SYSTEM WITH BREAK RETEST
// =============================================================================
alertcondition(strongBullishSignal, "Strong Bullish Signal", "Strong Bullish Signal Detected - High Confidence")
alertcondition(weakBullishSignal, "Weak Bullish Signal", "Weak Bullish Signal Detected")
alertcondition(strongBearishSignal, "Strong Bearish Signal", "Strong Bearish Signal Detected - High Confidence")
alertcondition(weakBearishSignal, "Weak Bearish Signal", "Weak Bearish Signal Detected")
alertcondition(math.abs(finalSignal) > 1.5, "Extreme Signal", "Extreme Market Condition Detected")
alertcondition(ta.crossover(finalSignal, 0), "Zero Line Cross Up", "Signal Crossed Above Zero Line")
alertcondition(ta.crossunder(finalSignal, 0), "Zero Line Cross Down", "Signal Crossed Below Zero Line")

// Enhanced Break Retest specific alerts with strength classification
alertcondition(resistance_break_signal, "Resistance Break", "Resistance Level Broken - Bullish Breakout")
alertcondition(support_break_signal, "Support Break", "Support Level Broken - Bearish Breakdown")
alertcondition(resistance_retest_signal, "Resistance Retest", "Resistance Level Retested - Potential Reversal")
alertcondition(support_retest_signal, "Support Retest", "Support Level Retested - Potential Bounce")
alertcondition(breakBullishSignal or breakBearishSignal, "Break Signal", "Break Signal Detected - Structure Broken")
alertcondition(retestBullishSignal or retestBearishSignal, "Retest Signal", "Retest Signal Detected - Structure Retested")

// High-confidence alerts for strong levels
alertcondition(strong_resistance_break, "STRONG Resistance Break", "HIGH CONFIDENCE: Strong Resistance Broken with Volume!")
alertcondition(strong_support_break, "STRONG Support Break", "HIGH CONFIDENCE: Strong Support Broken with Volume!")
alertcondition(weak_break_warning, "Weak Break Warning", "WARNING: Break detected but with low volume - may be false breakout")

// =============================================================================
// 📊 NEON INFORMATION PANEL (OPTIONAL)
// =============================================================================
showInfoTable = input.bool(false, "Show Neon Info Panel", tooltip="Displays a live information panel showing:\n• Current resistance/support levels\n• Break/retest status\n• Signal strength\nUseful for monitoring key levels in real-time!", group="🌟 Neon Display")

if showInfoTable and barstate.islast
    // Neon-themed table with dark background and glowing borders
    var table infoTable = table.new(position.top_right, 2, 6,
                                   bgcolor=color.new(shadow_col, 10),
                                   border_width=2,
                                   border_color=color.new(break_col, 30))

    table.cell(infoTable, 0, 0, "🌟 NEON STATUS PANEL", text_color=glow_col, text_size=size.normal, bgcolor=color.new(accent_col, 80))
    table.cell(infoTable, 1, 0, "", text_color=glow_col, text_size=size.small, bgcolor=color.new(accent_col, 80))

    table.cell(infoTable, 0, 1, "⚡ Resistance", text_color=color.new(break_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    resistance_text = str.tostring(recent_resistance, "#.####") + " (💪" + str.tostring(resistance_level_strength) + ")"
    table.cell(infoTable, 1, 1, resistance_text, text_color=glow_col, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 2, "⚡ Support", text_color=color.new(retest_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    support_text = str.tostring(recent_support, "#.####") + " (💪" + str.tostring(support_level_strength) + ")"
    table.cell(infoTable, 1, 2, support_text, text_color=glow_col, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 3, "🔥 R-Status", text_color=color.new(upper_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    res_status = resistance_broken ? (resistance_retested ? "💥 BROKEN & RETESTED" : strong_resistance_break ? "🚀 STRONG BREAK" : "💥 BROKEN") : "✨ INTACT"
    res_color = resistance_broken ? (strong_resistance_break ? color.new(break_col, 0) : color.new(lower_col, 0)) : color.new(upper_col, 0)
    table.cell(infoTable, 1, 3, res_status, text_color=res_color, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 4, "🔥 S-Status", text_color=color.new(lower_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    sup_status = support_broken ? (support_retested ? "💥 BROKEN & RETESTED" : strong_support_break ? "� STRONG BREAK" : "�💥 BROKEN") : "✨ INTACT"
    sup_color = support_broken ? (strong_support_break ? color.new(break_col, 0) : color.new(lower_col, 0)) : color.new(upper_col, 0)
    table.cell(infoTable, 1, 4, sup_status, text_color=sup_color, text_size=size.small, bgcolor=color.new(shadow_col, 20))

    table.cell(infoTable, 0, 5, "⚡ Signal Power", text_color=color.new(neutral_col, 0), text_size=size.small, bgcolor=color.new(shadow_col, 20))
    signal_strength_text = str.tostring(math.abs(br_signal), "#.##") + " ⚡ (Vol:" + str.tostring(volume_strength, "#.#") + ")"
    table.cell(infoTable, 1, 5, signal_strength_text, text_color=glow_col, text_size=size.small, bgcolor=color.new(shadow_col, 20))